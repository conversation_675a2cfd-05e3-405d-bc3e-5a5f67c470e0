using System;
using System.Text;
using Fpi.DB;
using Fpi.HB.Business.HisData;
using Fpi.WMS3000.Equipment.Config;

namespace Fpi.WMS3900.Pollution.DB
{
    /// <summary>
    /// 存储数据帮助类
    /// </summary>
    public static class SaveDataHelper
    {
        #region 属性字段

        private static readonly object lockObj = new object();

        #endregion

        #region 数据分类

        #region 周期数据

        /// <summary>
        /// 写污染源数据到数据库
        /// </summary>
        public static void WritePollutionDataToDb(DateTime time, ePollutionDataType dataType)
        {
            lock(lockObj)
            {
                _savePollutionData(time, (int)dataType);
            }
        }

        /// <summary>
        /// 写污染源数据到数据库
        /// </summary>
        /// <param name="time"></param>
        private static void _savePollutionData(DateTime time, int dataType)
        {
            string searchSql = $"select count(*) from {DbConfig.POLLUTION_DATA_TABLE} where datatime='{time.ToString(DbConfig.DATETIME_FORMAT)}' and datatype='{dataType}'";
            int count = DbAccess.QueryRecordCount(searchSql);
            // 如果之前存在相同时间戳及类型的数据，则跳过
            if(count > 0)
            {
                return;
            }
            //插入新数据
            InsertPollutionData(time, dataType);
        }

        private static void InsertPollutionData(DateTime time, int dataType)
        {
            var valueNodeList = ReportManager.GetInstance().GetFirstQueryGroup().GetQueryGroupValueNode();

            var sb = new StringBuilder();
            sb.Append("insert into ").Append(DbConfig.POLLUTION_DATA_TABLE).Append($"(datatime,datatype");

            // 各统计量因子
            QueryGroup queryGroup = ReportManager.GetInstance().GetFirstQueryGroup();
            if(queryGroup != null)
            {
                // 各测量点及数据标志字段名
                foreach(var valueNode in valueNodeList)
                {
                    string valueNodeId = valueNode.id;
                    sb.Append(", ").Append(DbConfig.PREFIX_F).Append(valueNodeId);
                    sb.Append(", ").Append(DbConfig.PREFIX_F).Append(valueNodeId).Append(DbConfig.POSTFIX);
                }
            }

            sb.Append($",{DbConfig.TOTAL_FLOW}) values('").Append(time.ToString(DbConfig.DATETIME_FORMAT)).Append("', ").Append(dataType);

            // 各测量点及数据标志值
            foreach(var valueNode in valueNodeList)
            {
                sb.Append(", ").Append(valueNode.GetValue().ToDBSaveFormat());
                sb.Append(", ").Append(valueNode.State);
            }

            // 累计流量因子ID
            var totalFlowNodeId = ExterEquipConfigManager.GetInstance().PollutionDataSaveConfigInfo.TotalFlowNodeId;

            // 查询时段内累计流量，如果没有，则为0


            sb.Append(")");
            DbAccess.ExecuteNonQuery(sb.ToString());
        }

        #endregion

        #endregion

        #region 辅助方法

        /// <summary>
        /// 转换为数据库存储格式
        /// </summary>
        /// <param name="value"></param>
        /// <returns></returns>
        private static string ToDBSaveFormat(this double value)
        {
            return (double.IsNaN(value) || double.IsInfinity(value)) ? "null" : value.ToString("G10");
        }

        #endregion
    }
}