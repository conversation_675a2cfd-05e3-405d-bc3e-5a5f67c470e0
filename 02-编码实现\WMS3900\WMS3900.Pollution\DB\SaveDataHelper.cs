using System;
using System.Text;
using Fpi.Data.Config;
using Fpi.DB;
using Fpi.DB.Manager;
using Fpi.DB.SqlUtil;
using Fpi.HB.Business.HisData;
using Fpi.WMS3000.Equipment;
using Fpi.WMS3000.Equipment.Config;

namespace Fpi.WMS3900.Pollution.DB
{
    /// <summary>
    /// 存储数据帮助类
    /// </summary>
    public static class SaveDataHelper
    {
        #region 属性字段

        private static readonly object lockObj = new object();

        #endregion

        #region 数据存储

        #region 周期数据

        /// <summary>
        /// 写污染源数据到数据库
        /// </summary>
        public static void WritePollutionDataToDb(DateTime time, ePollutionDataType dataType)
        {
            lock(lockObj)
            {
                _savePollutionData(time, dataType);
            }
        }

        /// <summary>
        /// 写污染源数据到数据库
        /// </summary>
        /// <param name="time"></param>
        private static void _savePollutionData(DateTime time, ePollutionDataType dataType)
        {
            string searchSql = $"select count(*) from {DbConfig.POLLUTION_MEASURE_DATA_TABLE} where datatime='{time.ToString(DbConfig.DATETIME_FORMAT)}' and datatype='{(int)dataType}'";
            int count = DbAccess.QueryRecordCount(searchSql);
            // 如果之前存在相同时间戳及类型的数据，则跳过
            if(count > 0)
            {
                return;
            }

            switch(dataType)
            {
                case ePollutionDataType.实时数据:
                    // 插入30秒数据
                    InsertSecondPollutionData(time, (int)dataType);
                    break;
                case ePollutionDataType.分钟数据:
                    break;
                case ePollutionDataType.十分钟数据:
                    break;
                case ePollutionDataType.小时数据:
                    break;
                case ePollutionDataType.日数据:
                    break;
                default:
                    break;
            }
        }

        /// <summary>
        /// 插入30秒数据
        /// </summary>
        /// <param name="time"></param>
        /// <param name="dataType"></param>
        private static void InsertSecondPollutionData(DateTime time, int dataType)
        {
            // 获取所有待存储的因子
            var valueNodeList = ReportManager.GetInstance().GetFirstQueryGroup().GetQueryGroupValueNode();
            // 累计流量因子ID
            var totalFlowNodeId = ExterEquipConfigManager.GetInstance().PollutionDataSaveConfigInfo.TotalFlowNodeId;
            // 当前累计流量值
            double currenTotalFlowValue = double.NaN;

            var sb = new StringBuilder();
            sb.Append("insert into ").Append(DbConfig.POLLUTION_MEASURE_DATA_TABLE).Append($"(datatime,datatype");

            // 各统计量因子
            QueryGroup queryGroup = ReportManager.GetInstance().GetFirstQueryGroup();
            if(queryGroup != null)
            {
                // 各测量点及数据标志字段名
                foreach(var valueNode in valueNodeList)
                {
                    string valueNodeId = valueNode.id;
                    sb.Append(", ").Append(DbConfig.PREFIX_F).Append(valueNodeId);
                    sb.Append(", ").Append(DbConfig.PREFIX_F).Append(valueNodeId).Append(DbConfig.POSTFIX);
                }
            }

            sb.Append($",{DbConfig.TOTAL_FLOW}) values('").Append(time.ToString(DbConfig.DATETIME_FORMAT)).Append("', ").Append(dataType);

            // 各测量点及数据标志值
            foreach(var valueNode in valueNodeList)
            {
                var value = valueNode.GetValue();
                sb.Append(", ").Append(value.ToDBSaveFormat());
                sb.Append(", ").Append(valueNode.State);

                // 记录累计流量因子当前值
                if(totalFlowNodeId == valueNode.id)
                {
                    currenTotalFlowValue = value;
                }
            }

            double totalFlowValue = double.NaN;

            // 计算时段内累计流量差值
            if(!double.IsNaN(currenTotalFlowValue))
            {
                totalFlowValue = CalculateTotalFlowDifference(time, dataType, totalFlowNodeId, currenTotalFlowValue);
            }

            sb.Append(", ").Append(totalFlowValue.ToDBSaveFormat()).Append(")");

            DbAccess.ExecuteNonQuery(sb.ToString());
        }

        #endregion

        /// <summary>
        /// 计算累计流量差值
        /// </summary>
        /// <param name="time">当前时间</param>
        /// <param name="dataType">数据类型</param>
        /// <param name="totalFlowNodeId">累计流量因子ID</param>
        /// <param name="totalFlowNodeId">当前累计流量</param>
        /// <returns>累计流量差值</returns>
        private static double CalculateTotalFlowDifference(DateTime time, int dataType, string totalFlowNodeId, double currenTotalFlowValue)
        {
            try
            {
                // 如果累计流量因子ID为空，或者当前累计流量值无效，返回0
                if(string.IsNullOrEmpty(totalFlowNodeId) || double.IsNaN(currenTotalFlowValue) || double.IsInfinity(currenTotalFlowValue))
                {
                    return 0;
                }

                // 获取上一周期的累计流量值
                double previousTotalFlow = GetPreviousTotalFlow(time, dataType, totalFlowNodeId);
                if(double.IsNaN(previousTotalFlow) || double.IsInfinity(previousTotalFlow))
                {
                    return 0;
                }

                // 计算差值
                double difference = currenTotalFlowValue - previousTotalFlow;

                // 如果差值为负数（可能是累计流量重置），返回0
                return difference >= 0 ? difference : 0;
            }
            catch
            {
                // 异常情况返回0
                return 0;
            }
        }

        /// <summary>
        /// 获取上一周期的累计流量值
        /// </summary>
        /// <param name="currentTime">当前时间</param>
        /// <param name="dataType">数据类型</param>
        /// <param name="totalFlowNodeId">累计流量因子ID</param>
        /// <returns>上一周期累计流量值</returns>
        private static double GetPreviousTotalFlow(DateTime currentTime, int dataType, string totalFlowNodeId)
        {
            try
            {
                var previousTotalFlow = double.NaN;

                // 计算上一周期时间
                DateTime previousTime = GetPreviousCycleTime(currentTime, dataType);

                FpiTable table = FpiDataBase.GetInstance().FindTableByName(DbConfig.POLLUTION_MEASURE_DATA_TABLE);
                if(table != null)
                {
                    lock(lockObj)
                    {
                        SearchConditionCollection condition = new SearchConditionCollection
                    {
                        new SearchCondition("datatime", new ColumnComparison(SqlOperator.Equal, previousTime)),
                        new SearchCondition("datatype", new ColumnComparison(SqlOperator.Equal,dataType))
                    };

                        var result = table.Search(condition);
                        if(result.Count > 0)
                        {
                            try
                            {
                                previousTotalFlow = Convert.ToSingle(result[0].GetFieldValue($"{DbConfig.PREFIX_F}{totalFlowNodeId}"));
                            }
                            catch
                            {
                                // ignore
                            }
                        }
                    }
                }

                return previousTotalFlow;
            }
            catch(Exception)
            {
                return double.NaN;
            }
        }

        /// <summary>
        /// 检查数据是否已存在
        /// </summary>
        /// <param name="time">时间</param>
        /// <param name="dataType">数据类型</param>
        /// <returns>是否存在</returns>
        public static bool IsDataExists(DateTime time, ePollutionDataType dataType)
        {
            try
            {
                string searchSql = $"select count(*) from {DbConfig.POLLUTION_MEASURE_DATA_TABLE} where datatime='{time.ToString(DbConfig.DATETIME_FORMAT)}' and datatype='{(int)dataType}'";
                int count = DbAccess.QueryRecordCount(searchSql);
                return count > 0;
            }
            catch (Exception)
            {
                return false;
            }
        }

        /// <summary>
        /// 写计算后的污染源数据到数据库
        /// </summary>
        /// <param name="time">时间</param>
        /// <param name="dataType">数据类型</param>
        public static void WritePollutionCalculatedDataToDb(DateTime time, ePollutionDataType dataType)
        {
            lock(lockObj)
            {
                _saveCalculatedPollutionData(time, (int)dataType);
            }
        }

        /// <summary>
        /// 保存计算后的污染源数据
        /// </summary>
        /// <param name="time">时间</param>
        /// <param name="dataType">数据类型</param>
        private static void _saveCalculatedPollutionData(DateTime time, int dataType)
        {
            // 检查数据是否已存在
            if (IsDataExists(time, (ePollutionDataType)dataType))
            {
                return;
            }

            // 插入计算后的数据
            InsertCalculatedPollutionData(time, dataType);
        }

        /// <summary>
        /// 插入计算后的污染源数据
        /// </summary>
        /// <param name="time">时间</param>
        /// <param name="dataType">数据类型</param>
        private static void InsertCalculatedPollutionData(DateTime time, int dataType)
        {
            try
            {
                var queryGroup = ReportManager.GetInstance().GetFirstQueryGroup();
                if (queryGroup == null) return;

                var valueNodeList = queryGroup.GetQueryGroupValueNode();
                if (valueNodeList == null || valueNodeList.Count == 0) return;

                var sb = new StringBuilder();
                sb.Append("insert into ").Append(DbConfig.POLLUTION_MEASURE_DATA_TABLE).Append($"(datatime,datatype");

                // 各统计量因子字段名
                foreach (var valueNode in valueNodeList)
                {
                    string valueNodeId = valueNode.id;
                    sb.Append(", ").Append(DbConfig.PREFIX_F).Append(valueNodeId);
                    sb.Append(", ").Append(DbConfig.PREFIX_F).Append(valueNodeId).Append(DbConfig.POSTFIX);
                }

                sb.Append($",{DbConfig.TOTAL_FLOW}) values('").Append(time.ToString(DbConfig.DATETIME_FORMAT)).Append("', ").Append(dataType);

                // 获取配置信息
                var config = ExterEquipConfigManager.GetInstance().PollutionDataSaveConfigInfo;
                var calculateMethod = GetCalculateMethodForDataType((ePollutionDataType)dataType, config);

                // 计算各因子数据
                foreach (var valueNode in valueNodeList)
                {
                    var calculatedData = CalculateFactorData(valueNode, time, (ePollutionDataType)dataType, calculateMethod);
                    sb.Append(", ").Append(calculatedData.Value.ToDBSaveFormat());
                    sb.Append(", ").Append(calculatedData.State);
                }

                // 计算时段流量
                double totalFlowValue = CalculatePeriodTotalFlow(time, (ePollutionDataType)dataType, config.TotalFlowNodeId);
                sb.Append(", ").Append(totalFlowValue.ToDBSaveFormat());

                sb.Append(")");
                DbAccess.ExecuteNonQuery(sb.ToString());
            }
            catch (Exception ex)
            {
                // 记录错误日志但不抛出异常，确保系统稳定运行
                // LogHelper.WriteErrorLog($"插入计算数据失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取数据类型对应的计算方法
        /// </summary>
        /// <param name="dataType">数据类型</param>
        /// <param name="config">配置信息</param>
        /// <returns>计算方法</returns>
        private static eDataCalculateMethod GetCalculateMethodForDataType(ePollutionDataType dataType, PollutionDataSaveConfig config)
        {
            switch (dataType)
            {
                case ePollutionDataType.分钟数据:
                case ePollutionDataType.十分钟数据:
                case ePollutionDataType.小时数据:
                    return config.MinuteDataCalculateMethod;
                case ePollutionDataType.日数据:
                    return config.DayDataCalculateMethod;
                default:
                    return eDataCalculateMethod.实时值; // 默认实时值
            }
        }

        /// <summary>
        /// 计算因子数据
        /// </summary>
        /// <param name="valueNode">因子节点</param>
        /// <param name="time">时间</param>
        /// <param name="dataType">数据类型</param>
        /// <param name="calculateMethod">计算方法</param>
        /// <returns>计算结果</returns>
        private static (double Value, int State) CalculateFactorData(ValueNode valueNode, DateTime time, ePollutionDataType dataType, eDataCalculateMethod calculateMethod)
        {
            try
            {
                // 累计流量因子特殊处理
                var totalFlowNodeId = ExterEquipConfigManager.GetInstance().PollutionDataSaveConfigInfo.TotalFlowNodeId;
                if (!string.IsNullOrEmpty(totalFlowNodeId) && valueNode.id == totalFlowNodeId)
                {
                    return CalculateTotalFlowFactorData(valueNode, time, dataType);
                }

                // 常规因子处理
                switch (calculateMethod)
                {
                    case eDataCalculateMethod.实时值:
                        return (valueNode.GetValue(), valueNode.State);
                    case eDataCalculateMethod.算数平均:
                        return CalculateArithmeticAverage(valueNode, time, dataType);
                    case eDataCalculateMethod.加权平均:
                        return CalculateWeightedAverage(valueNode, time, dataType);
                    default:
                        return (valueNode.GetValue(), valueNode.State);
                }
            }
            catch (Exception)
            {
                return (double.NaN, (int)eValueNodeState.B);
            }
        }

        /// <summary>
        /// 计算时段流量
        /// </summary>
        /// <param name="time">时间</param>
        /// <param name="dataType">数据类型</param>
        /// <param name="totalFlowNodeId">累计流量因子ID</param>
        /// <returns>时段流量</returns>
        private static double CalculatePeriodTotalFlow(DateTime time, ePollutionDataType dataType, string totalFlowNodeId)
        {
            try
            {
                if (string.IsNullOrEmpty(totalFlowNodeId))
                    return 0;

                // 获取时段范围
                var (startTime, endTime) = GetPeriodRange(time, dataType);

                // 查询头尾30秒数据中的累计流量
                double startFlow = GetTotalFlowFromRealData(startTime, totalFlowNodeId);
                double endFlow = GetTotalFlowFromRealData(endTime, totalFlowNodeId);

                if (double.IsNaN(startFlow) || double.IsNaN(endFlow))
                    return 0;

                double difference = endFlow - startFlow;
                return difference >= 0 ? difference : 0;
            }
            catch (Exception)
            {
                return 0;
            }
        }

        #endregion

        #region 辅助方法

        /// <summary>
        /// 获取上一周期时间
        /// </summary>
        /// <param name="currentTime">当前时间</param>
        /// <param name="dataType">数据类型</param>
        /// <returns>上一周期时间</returns>
        private static DateTime GetPreviousCycleTime(DateTime currentTime, int dataType)
        {
            switch((ePollutionDataType)dataType)
            {
                case ePollutionDataType.实时数据: // 30秒数据
                    return currentTime.AddSeconds(-30);
                case ePollutionDataType.分钟数据: // 1分钟数据
                    return currentTime.AddMinutes(-1);
                case ePollutionDataType.十分钟数据: // 1分钟数据
                    return currentTime.AddMinutes(-10);
                case ePollutionDataType.小时数据: // 小时数据
                    return currentTime.AddHours(-1);
                case ePollutionDataType.日数据: // 日数据
                    return currentTime.AddDays(-1);
                default:
                    return currentTime.AddSeconds(-30);
            }
        }

        /// <summary>
        /// 获取时段范围
        /// </summary>
        /// <param name="time">时间</param>
        /// <param name="dataType">数据类型</param>
        /// <returns>开始时间和结束时间</returns>
        private static (DateTime StartTime, DateTime EndTime) GetPeriodRange(DateTime time, ePollutionDataType dataType)
        {
            switch (dataType)
            {
                case ePollutionDataType.分钟数据:
                    // 1点0分的数据取1点0分30秒至1点1分0秒的数据
                    return (time.AddSeconds(30), time.AddMinutes(1));
                case ePollutionDataType.十分钟数据:
                    // 1点0分的数据取1点0分30秒至1点10分0秒的数据
                    return (time.AddSeconds(30), time.AddMinutes(10));
                case ePollutionDataType.小时数据:
                    // 1点的数据取1点0分至1点50分的10分钟数据
                    return (time, time.AddMinutes(50));
                case ePollutionDataType.日数据:
                    // 12日的数据取12日0点至12日23点的小时数据
                    return (time, time.AddHours(23));
                default:
                    return (time, time);
            }
        }

        /// <summary>
        /// 转换为数据库存储格式
        /// </summary>
        /// <param name="value"></param>
        /// <returns></returns>
        private static string ToDBSaveFormat(this double value)
        {
            return (double.IsNaN(value) || double.IsInfinity(value)) ? "null" : value.ToString("G10");
        }

        #endregion
    }
}