using System;
using System.Text;
using Fpi.DB;
using Fpi.DB.Manager;
using Fpi.DB.SqlUtil;
using Fpi.HB.Business.HisData;
using Fpi.WMS3000.Equipment.Config;

namespace Fpi.WMS3900.Pollution.DB
{
    /// <summary>
    /// 存储数据帮助类
    /// </summary>
    public static class SaveDataHelper
    {
        #region 属性字段

        private static readonly object lockObj = new object();

        #endregion

        #region 数据存储

        #region 周期数据

        /// <summary>
        /// 写污染源数据到数据库
        /// </summary>
        public static void WritePollutionDataToDb(DateTime time, ePollutionDataType dataType)
        {
            lock(lockObj)
            {
                _savePollutionData(time, dataType);
            }
        }

        /// <summary>
        /// 写污染源数据到数据库
        /// </summary>
        /// <param name="time"></param>
        private static void _savePollutionData(DateTime time, ePollutionDataType dataType)
        {
            string searchSql = $"select count(*) from {DbConfig.POLLUTION_MEASURE_DATA_TABLE} where datatime='{time.ToString(DbConfig.DATETIME_FORMAT)}' and datatype='{(int)dataType}'";
            int count = DbAccess.QueryRecordCount(searchSql);
            // 如果之前存在相同时间戳及类型的数据，则跳过
            if(count > 0)
            {
                return;
            }

            switch(dataType)
            {
                case ePollutionDataType.实时数据:
                    // 插入30秒数据
                    InsertSecondPollutionData(time, (int)dataType);
                    break;
                case ePollutionDataType.分钟数据:
                    break;
                case ePollutionDataType.十分钟数据:
                    break;
                case ePollutionDataType.小时数据:
                    break;
                case ePollutionDataType.日数据:
                    break;
                default:
                    break;
            }
        }

        /// <summary>
        /// 插入30秒数据
        /// </summary>
        /// <param name="time"></param>
        /// <param name="dataType"></param>
        private static void InsertSecondPollutionData(DateTime time, int dataType)
        {
            // 获取所有待存储的因子
            var valueNodeList = ReportManager.GetInstance().GetFirstQueryGroup().GetQueryGroupValueNode();
            // 累计流量因子ID
            var totalFlowNodeId = ExterEquipConfigManager.GetInstance().PollutionDataSaveConfigInfo.TotalFlowNodeId;
            // 当前累计流量值
            double currenTotalFlowValue = double.NaN;

            var sb = new StringBuilder();
            sb.Append("insert into ").Append(DbConfig.POLLUTION_MEASURE_DATA_TABLE).Append($"(datatime,datatype");

            // 各统计量因子
            QueryGroup queryGroup = ReportManager.GetInstance().GetFirstQueryGroup();
            if(queryGroup != null)
            {
                // 各测量点及数据标志字段名
                foreach(var valueNode in valueNodeList)
                {
                    string valueNodeId = valueNode.id;
                    sb.Append(", ").Append(DbConfig.PREFIX_F).Append(valueNodeId);
                    sb.Append(", ").Append(DbConfig.PREFIX_F).Append(valueNodeId).Append(DbConfig.POSTFIX);
                }
            }

            sb.Append($",{DbConfig.TOTAL_FLOW}) values('").Append(time.ToString(DbConfig.DATETIME_FORMAT)).Append("', ").Append(dataType);

            // 各测量点及数据标志值
            foreach(var valueNode in valueNodeList)
            {
                var value = valueNode.GetValue();
                sb.Append(", ").Append(value.ToDBSaveFormat());
                sb.Append(", ").Append(valueNode.State);

                // 记录累计流量因子当前值
                if(totalFlowNodeId == valueNode.id)
                {
                    currenTotalFlowValue = value;
                }
            }

            double totalFlowValue = double.NaN;

            // 计算时段内累计流量差值
            if(!double.IsNaN(currenTotalFlowValue))
            {
                totalFlowValue = CalculateTotalFlowDifference(time, dataType, totalFlowNodeId, currenTotalFlowValue);
            }

            sb.Append(", ").Append(totalFlowValue.ToDBSaveFormat()).Append(")");

            DbAccess.ExecuteNonQuery(sb.ToString());
        }

        #endregion

        /// <summary>
        /// 计算累计流量差值
        /// </summary>
        /// <param name="time">当前时间</param>
        /// <param name="dataType">数据类型</param>
        /// <param name="totalFlowNodeId">累计流量因子ID</param>
        /// <param name="totalFlowNodeId">当前累计流量</param>
        /// <returns>累计流量差值</returns>
        private static double CalculateTotalFlowDifference(DateTime time, int dataType, string totalFlowNodeId, double currenTotalFlowValue)
        {
            try
            {
                // 如果累计流量因子ID为空，或者当前累计流量值无效，返回0
                if(string.IsNullOrEmpty(totalFlowNodeId) || double.IsNaN(currenTotalFlowValue) || double.IsInfinity(currenTotalFlowValue))
                {
                    return 0;
                }

                // 获取上一周期的累计流量值
                double previousTotalFlow = GetPreviousTotalFlow(time, dataType, totalFlowNodeId);
                if(double.IsNaN(previousTotalFlow) || double.IsInfinity(previousTotalFlow))
                {
                    return 0;
                }

                // 计算差值
                double difference = currenTotalFlowValue - previousTotalFlow;

                // 如果差值为负数（可能是累计流量重置），返回0
                return difference >= 0 ? difference : 0;
            }
            catch
            {
                // 异常情况返回0
                return 0;
            }
        }

        /// <summary>
        /// 获取上一周期的累计流量值
        /// </summary>
        /// <param name="currentTime">当前时间</param>
        /// <param name="dataType">数据类型</param>
        /// <param name="totalFlowNodeId">累计流量因子ID</param>
        /// <returns>上一周期累计流量值</returns>
        private static double GetPreviousTotalFlow(DateTime currentTime, int dataType, string totalFlowNodeId)
        {
            try
            {
                var previousTotalFlow = double.NaN;

                // 计算上一周期时间
                DateTime previousTime = GetPreviousCycleTime(currentTime, dataType);

                FpiTable table = FpiDataBase.GetInstance().FindTableByName(DbConfig.POLLUTION_MEASURE_DATA_TABLE);
                if(table != null)
                {
                    lock(lockObj)
                    {
                        SearchConditionCollection condition = new SearchConditionCollection
                    {
                        new SearchCondition("datatime", new ColumnComparison(SqlOperator.Equal, previousTime)),
                        new SearchCondition("datatype", new ColumnComparison(SqlOperator.Equal,dataType))
                    };

                        var result = table.Search(condition);
                        if(result.Count > 0)
                        {
                            try
                            {
                                previousTotalFlow = Convert.ToSingle(result[0].GetFieldValue($"{DbConfig.PREFIX_F}{totalFlowNodeId}"));
                            }
                            catch
                            {
                                // ignore
                            }
                        }
                    }
                }

                return previousTotalFlow;
            }
            catch(Exception)
            {
                return double.NaN;
            }
        }

        #endregion

        #region 辅助方法

        /// <summary>
        /// 获取上一周期时间
        /// </summary>
        /// <param name="currentTime">当前时间</param>
        /// <param name="dataType">数据类型</param>
        /// <returns>上一周期时间</returns>
        private static DateTime GetPreviousCycleTime(DateTime currentTime, int dataType)
        {
            switch((ePollutionDataType)dataType)
            {
                case ePollutionDataType.实时数据: // 30秒数据
                    return currentTime.AddSeconds(-30);
                case ePollutionDataType.分钟数据: // 1分钟数据
                    return currentTime.AddMinutes(-1);
                case ePollutionDataType.十分钟数据: // 1分钟数据
                    return currentTime.AddMinutes(-10);
                case ePollutionDataType.小时数据: // 小时数据
                    return currentTime.AddHours(-1);
                case ePollutionDataType.日数据: // 日数据
                    return currentTime.AddDays(-1);
                default:
                    return currentTime.AddSeconds(-30);
            }
        }

        /// <summary>
        /// 转换为数据库存储格式
        /// </summary>
        /// <param name="value"></param>
        /// <returns></returns>
        private static string ToDBSaveFormat(this double value)
        {
            return (double.IsNaN(value) || double.IsInfinity(value)) ? "null" : value.ToString("G10");
        }

        #endregion
    }
}