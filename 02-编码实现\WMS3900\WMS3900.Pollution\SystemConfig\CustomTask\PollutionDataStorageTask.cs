using Fpi.HB.Business.Tasks;
using System;

namespace Fpi.WMS3000.Pollution.SystemConfig
{
    /// <summary>
    /// 污染源数据存储任务
    /// 30秒、1分钟、10分钟、小时、日数据存储
    /// </summary>
    public class PollutionDataStorageTask : CustomTask
    {
        #region 构造

        public PollutionDataStorageTask()
        {
            if(string.IsNullOrEmpty(this.Description))
            {
                this.Description = "污染源数据存储任务";
            }

            if(this.id.Contains("DefaultID"))
            {
                this.id = "PollutionDataStorageTask";
            }

            if(this.name.Contains("DefaultName"))
            {
                this.name = "污染源数据存储任务";
            }
        }

        #endregion

        #region 方法重写

        public override string ToString()
        {
            if(string.IsNullOrEmpty(this.name) || this.name.Contains("DefaultName"))
            {
                return "污染源数据存储任务";
            }
            else
            {
                return this.name;
            }
        }

        public override void DoTask()
        {
            DateTime now = DateTime.Now;

            // 每次运行，先判断本周期（30秒、1分钟）有没有存过数据。如果没有，则存储一次数据。
            // 根据分钟数是否为0来判断是否是整小时，小时和分钟是否都为0来判断是否是整天，分钟末尾是否为0来判断是否是整10分钟。如果是，则存储数据。
            // 存储数据时，调用初始配置来决定是使用算数平均还是加权平均。加权平均时，使用当前周期内的累计流量变化值作为权重。


            //// 整分存储
            //DBHelper.WriteRealDataToDb(now.AddSeconds(-now.Second));
        }

        #endregion
    }
}
