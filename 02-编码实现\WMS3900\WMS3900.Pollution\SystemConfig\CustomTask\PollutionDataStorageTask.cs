using System;
using Fpi.HB.Business.Tasks;
using Fpi.WMS3900.Pollution.DB;

namespace Fpi.WMS3000.Pollution.SystemConfig
{
    /// <summary>
    /// 污染源数据存储任务
    /// 30秒、1分钟、10分钟、小时、日数据存储
    /// </summary>
    public class PollutionDataStorageTask : CustomTask
    {
        #region 构造

        public PollutionDataStorageTask()
        {
            if(string.IsNullOrEmpty(this.Description))
            {
                this.Description = "污染源数据存储任务";
            }

            if(this.id.Contains("DefaultID"))
            {
                this.id = "PollutionDataStorageTask";
            }

            if(this.name.Contains("DefaultName"))
            {
                this.name = "污染源数据存储任务";
            }
        }

        #endregion

        #region 方法重写

        public override string ToString()
        {
            if(string.IsNullOrEmpty(this.name) || this.name.Contains("DefaultName"))
            {
                return "污染源数据存储任务";
            }
            else
            {
                return this.name;
            }
        }

        public override void DoTask()
        {
            // 获取当前时间
            DateTime now = DateTime.Now;

            // 30秒数据存储逻辑 - 每次运行都检查
            ProcessThirtySecondData(now);

            // 分钟数据存储逻辑 - 每分钟的前30秒内检查
            if(now.Second <= 30)
            {
                ProcessMinuteData(now);
            }

            // 10分钟数据存储逻辑 - 每个整10分钟的前30秒内检查
            if(now.Minute % 10 == 0 && now.Second <= 30)
            {
                ProcessTenMinuteData(now);
            }

            // 小时数据存储逻辑 - 每小时第一分钟的前30秒内检查
            if(now.Minute == 0 && now.Second <= 30)
            {
                ProcessHourData(now);
            }

            // 日数据存储逻辑 - 每日第一小时第一分钟的前30秒内检查
            if(now.Hour == 0 && now.Minute == 0 && now.Second <= 30)
            {
                ProcessDayData(now);
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 处理30秒数据存储
        /// </summary>
        /// <param name="now">当前时间</param>
        private void ProcessThirtySecondData(DateTime now)
        {
            // 计算整30秒时间戳（00秒或30秒）
            DateTime thirtySecondTime = new DateTime(now.Year, now.Month, now.Day, now.Hour, now.Minute, now.Second < 30 ? 0 : 30);

            // 存储30秒数据（实时数据）
            SaveDataHelper.WritePollutionDataToDb(thirtySecondTime, ePollutionDataType.实时数据);
        }

        /// <summary>
        /// 处理分钟数据存储
        /// </summary>
        /// <param name="now">当前时间</param>
        private void ProcessMinuteData(DateTime now)
        {
            // 计算前一分钟时间戳（头标法）
            DateTime minuteTime = now.AddSeconds(-now.Second).AddMinutes(-1);

            // 存储前一分钟时间
            SaveDataHelper.WritePollutionCalculatedDataToDb(minuteTime, ePollutionDataType.分钟数据);
        }

        /// <summary>
        /// 处理10分钟数据存储
        /// </summary>
        /// <param name="now">当前时间</param>
        private void ProcessTenMinuteData(DateTime now)
        {
            // 计算10分钟时间戳（头标法）
            DateTime tenMinuteTime = now.AddSeconds(-now.Second).AddMinutes(-(now.Minute % 10) - 10);

            // 存储前十分钟数据
            SaveDataHelper.WritePollutionCalculatedDataToDb(tenMinuteTime, ePollutionDataType.十分钟数据);
        }

        /// <summary>
        /// 处理小时数据存储
        /// </summary>
        /// <param name="now">当前时间</param>
        private void ProcessHourData(DateTime now)
        {
            // 计算小时时间戳（头标法）
            DateTime hourTime = now.AddSeconds(-now.Second).AddMinutes(-now.Minute).AddHours(-1);

            // 存储前一小时数据
            SaveDataHelper.WritePollutionCalculatedDataToDb(hourTime, ePollutionDataType.小时数据);
        }

        /// <summary>
        /// 处理日数据存储
        /// </summary>
        /// <param name="now">当前时间</param>
        private void ProcessDayData(DateTime now)
        {
            // 计算日时间戳（头标法）
            DateTime dayTime = now.AddSeconds(-now.Second).AddMinutes(-now.Minute).AddHours(-now.Hour).AddDays(-1);

            // 存储前一日数据
            SaveDataHelper.WritePollutionCalculatedDataToDb(dayTime, ePollutionDataType.日数据);
        }

        #endregion
    }
}