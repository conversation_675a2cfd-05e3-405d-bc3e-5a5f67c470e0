using Fpi.HB.Business.Tasks;
using Fpi.WMS3900.Pollution.DB;
using System;

namespace Fpi.WMS3000.Pollution.SystemConfig
{
    /// <summary>
    /// 污染源数据存储任务
    /// 30秒、1分钟、10分钟、小时、日数据存储
    /// </summary>
    public class PollutionDataStorageTask : CustomTask
    {
        #region 构造

        public PollutionDataStorageTask()
        {
            if(string.IsNullOrEmpty(this.Description))
            {
                this.Description = "污染源数据存储任务";
            }

            if(this.id.Contains("DefaultID"))
            {
                this.id = "PollutionDataStorageTask";
            }

            if(this.name.Contains("DefaultName"))
            {
                this.name = "污染源数据存储任务";
            }
        }

        #endregion

        #region 方法重写

        public override string ToString()
        {
            if(string.IsNullOrEmpty(this.name) || this.name.Contains("DefaultName"))
            {
                return "污染源数据存储任务";
            }
            else
            {
                return this.name;
            }
        }

        public override void DoTask()
        {
            DateTime now = DateTime.Now;

            // 30秒数据存储逻辑
            // 计算整30秒时间戳（00秒或30秒）
            DateTime thirtySecondTime = GetThirtySecondTime(now);

            // 存储30秒数据（实时数据）
            SaveDataHelper.WritePollutionDataToDb(thirtySecondTime, ePollutionDataType.实时数据);

            // 分钟数据存储
            if(now.Second == 0)
            {
                DateTime minuteTime = new DateTime(now.Year, now.Month, now.Day, now.Hour, now.Minute, 0);
                SaveDataHelper.WritePollutionDataToDb(minuteTime, ePollutionDataType.分钟数据);
            }

            // 十分钟数据存储
            if((now.Minute % 10) == 0)
            {
                DateTime minuteTime = new DateTime(now.Year, now.Month, now.Day, now.Hour, now.Minute, 0);
                SaveDataHelper.WritePollutionDataToDb(minuteTime, ePollutionDataType.十分钟数据);
            }

            // 整小时数据存储
            if(now.Minute == 0)
            {
                DateTime hourTime = now.AddSeconds(-now.Second); // 去掉秒数，保留整点时间
                SaveDataHelper.WritePollutionDataToDb(hourTime, ePollutionDataType.小时数据);
            }

            // 整天数据存储
            if(now.Hour == 0 && now.Minute == 0)
            {
                DateTime dayTime = now.AddSeconds(-now.Second); // 去掉秒数，保留整点时间
                SaveDataHelper.WritePollutionDataToDb(dayTime, ePollutionDataType.日数据);
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 获取整30秒时间戳
        /// </summary>
        /// <param name="currentTime">当前时间</param>
        /// <returns>整30秒时间戳</returns>
        private DateTime GetThirtySecondTime(DateTime currentTime)
        {
            // 将秒数调整为00秒或30秒
            int adjustedSecond = currentTime.Second < 30 ? 0 : 30;
            return new DateTime(currentTime.Year, currentTime.Month, currentTime.Day,
                               currentTime.Hour, currentTime.Minute, adjustedSecond);
        }

        #endregion
    }
}